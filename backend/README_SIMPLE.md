# 🚀 游戏后端简单部署指南

由于Docker环境问题，这里提供一个简单的本地部署方案。

## 📋 前提条件

确保以下服务已安装并运行：
- Python 3.11+
- MySQL 8.0+
- Redis 6.0+

## 🔧 启动步骤

### 1. 启动数据库服务
```bash
# 启动MySQL
sudo service mysql start

# 启动Redis  
sudo service redis-server start

# 检查服务状态
mysqladmin ping -h localhost -u root -p199211Hcy
redis-cli ping
```

### 2. 准备Python环境
```bash
cd backend

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 准备数据库
```bash
# 创建数据库（如果不存在）
mysql -h localhost -u root -p199211Hcy -e "CREATE DATABASE IF NOT EXISTS universe_vr_game CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
alembic upgrade head
```

### 4. 启动应用
```bash
# 启动游戏后端
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 📊 访问服务

启动成功后，您可以访问：
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **游戏API**: http://localhost:8000/api/v1/

## 🛠️ 一键启动脚本

如果您想要自动化这个过程：

```bash
# 启动本地服务
sudo backend/start-services.sh

# 启动应用
cd backend
./start-local.sh
```

## 🔍 故障排除

### MySQL连接问题
```bash
# 检查MySQL状态
sudo service mysql status

# 重启MySQL
sudo service mysql restart

# 测试连接
mysql -h localhost -u root -p199211Hcy -e "SHOW DATABASES;"
```

### Redis连接问题
```bash
# 检查Redis状态
sudo service redis-server status

# 重启Redis
sudo service redis-server restart

# 测试连接
redis-cli ping
```

### Python依赖问题
```bash
# 重新安装依赖
pip install --upgrade -r requirements.txt

# 检查Python版本
python3 --version
```

## 📝 配置说明

应用使用`.env`文件中的配置：
- 数据库: `mysql+aiomysql://root:199211Hcy@localhost:3306/universe_vr_game`
- Redis: `redis://localhost:6379/0`

这种方式虽然不使用Docker，但可以快速启动游戏后端进行开发和测试。
