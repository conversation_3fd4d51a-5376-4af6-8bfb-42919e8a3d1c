#!/bin/bash

# 游戏后端Docker启动脚本

set -e

echo "🚀 启动城市全景寻物游戏后端..."

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 启动Docker服务
echo "🔧 启动Docker服务..."
sudo service docker start || echo "Docker可能已在运行"

# 等待Docker守护进程启动
echo "⏳ 等待Docker守护进程启动..."
for i in {1..10}; do
    if sudo docker info &> /dev/null; then
        echo "✅ Docker守护进程已就绪"
        break
    fi
    echo "   等待Docker启动... ($i/10)"
    sleep 2
done

# 检查Docker是否可用
USE_SUDO=""
if docker info &> /dev/null; then
    echo "✅ Docker权限正常"
elif sudo docker info &> /dev/null; then
    echo "🔑 使用sudo权限运行Docker"
    USE_SUDO="sudo"
else
    echo "❌ Docker无法连接，尝试重启Docker服务..."
    sudo service docker stop
    sleep 3
    sudo service docker start
    sleep 5

    if sudo docker info &> /dev/null; then
        echo "✅ Docker重启成功"
        USE_SUDO="sudo"
    else
        echo "❌ Docker服务仍然无法连接"
        echo "请尝试以下解决方案："
        echo "1. 重启系统"
        echo "2. 重新安装Docker"
        echo "3. 检查系统日志: sudo journalctl -u docker"
        exit 1
    fi
fi

# 创建目录
mkdir -p logs

# 停止现有容器
echo "🛑 停止现有容器..."
$USE_SUDO docker-compose down --remove-orphans 2>/dev/null || true

# 构建并启动服务
echo "🔨 构建并启动服务..."
$USE_SUDO docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 检查服务状态
echo "🔍 检查服务状态..."
$USE_SUDO docker-compose ps

# 等待应用就绪
echo "⏳ 等待应用就绪..."
for i in {1..15}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 应用已就绪"
        break
    fi
    echo "   等待中... ($i/15)"
    sleep 3
done

# 运行数据库迁移
echo "🔄 运行数据库迁移..."
$USE_SUDO docker-compose exec game-backend alembic upgrade head || echo "⚠️  迁移可能失败"

# 测试API
echo "🧪 测试API..."
if curl -s http://localhost:8000/health | grep -q "healthy"; then
    echo "✅ API测试通过"
else
    echo "⚠️  API可能未完全就绪"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 访问信息："
echo "   - API: http://localhost:8000"
echo "   - 文档: http://localhost:8000/docs"
echo "   - 健康检查: http://localhost:8000/health"
echo ""
echo "🔧 管理命令："
if [ -n "$USE_SUDO" ]; then
    echo "   查看日志: sudo docker-compose logs -f"
    echo "   重启服务: sudo docker-compose restart"
    echo "   停止服务: sudo docker-compose down"
else
    echo "   查看日志: docker-compose logs -f"
    echo "   重启服务: docker-compose restart"
    echo "   停止服务: docker-compose down"
fi
echo ""
